// 注册调试工具
import { supabase } from '../lib/supabase';
import { AuthService } from '../services/authService';

export class RegistrationDebug {
  // 测试注册流程
  static async testRegistration(email: string, password: string, name: string) {
    console.group('🧪 测试注册流程');
    
    try {
      // 1. 检查邮箱是否已存在
      console.log('1. 检查邮箱是否已存在...');
      const { data: existingUsers, error: checkError } = await supabase
        .from('profiles')
        .select('email')
        .eq('email', email);
      
      if (checkError) {
        console.error('检查邮箱失败:', checkError);
      } else if (existingUsers && existingUsers.length > 0) {
        console.warn('⚠️ 邮箱已存在:', email);
        return { success: false, error: '邮箱已被注册' };
      } else {
        console.log('✅ 邮箱可用');
      }

      // 2. 尝试注册
      console.log('2. 开始注册流程...');
      const result = await AuthService.signUp(email, password, name);
      
      if (result.user) {
        console.log('✅ 用户创建成功:', {
          id: result.user.id,
          email: result.user.email,
          confirmed: result.user.email_confirmed_at ? true : false
        });

        // 3. 验证用户资料是否创建
        console.log('3. 验证用户资料...');
        await new Promise(resolve => setTimeout(resolve, 2000)); // 等待触发器执行
        
        const verification = await AuthService.verifyUserProfile(result.user.id);
        
        if (verification) {
          console.log('✅ 用户资料验证完成');
          return { 
            success: true, 
            user: result.user,
            verification 
          };
        } else {
          console.error('❌ 用户资料验证失败');
          return { 
            success: false, 
            error: '用户资料创建失败',
            user: result.user 
          };
        }
      } else {
        console.error('❌ 用户创建失败');
        return { success: false, error: '用户创建失败' };
      }
    } catch (error) {
      console.error('❌ 注册测试失败:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '未知错误' 
      };
    } finally {
      console.groupEnd();
    }
  }

  // 清理测试用户
  static async cleanupTestUser(email: string) {
    console.group('🧹 清理测试用户');
    
    try {
      // 注意：这个方法只能在开发环境使用
      if (import.meta.env.PROD) {
        console.warn('⚠️ 生产环境不允许清理用户');
        return;
      }

      // 1. 查找用户
      const { data: users, error: findError } = await supabase.auth.admin.listUsers();
      
      if (findError) {
        console.error('查找用户失败:', findError);
        return;
      }

      const user = users.users.find(u => u.email === email);
      
      if (!user) {
        console.log('用户不存在:', email);
        return;
      }

      console.log('找到用户:', user.id);

      // 2. 删除相关数据
      const userId = user.id;

      // 删除 user_balances
      await supabase.from('user_balances').delete().eq('user_id', userId);
      console.log('✅ 删除用户余额');

      // 删除 user_settings
      await supabase.from('user_settings').delete().eq('user_id', userId);
      console.log('✅ 删除用户设置');

      // 删除 profiles
      await supabase.from('profiles').delete().eq('id', userId);
      console.log('✅ 删除用户资料');

      // 3. 删除认证用户
      const { error: deleteError } = await supabase.auth.admin.deleteUser(userId);
      
      if (deleteError) {
        console.error('删除认证用户失败:', deleteError);
      } else {
        console.log('✅ 删除认证用户');
      }

      console.log('✅ 用户清理完成');
    } catch (error) {
      console.error('❌ 清理用户失败:', error);
    } finally {
      console.groupEnd();
    }
  }

  // 检查数据库状态
  static async checkDatabaseState() {
    console.group('📊 检查数据库状态');
    
    try {
      // 检查表是否存在
      const tables = ['profiles', 'user_settings', 'user_balances'];
      
      for (const table of tables) {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.error(`❌ 表 ${table} 检查失败:`, error);
        } else {
          console.log(`✅ 表 ${table} 正常`);
        }
      }

      // 检查触发器
      const { data: triggers, error: triggerError } = await supabase
        .rpc('check_triggers'); // 这个函数需要在数据库中创建

      if (triggerError) {
        console.log('触发器检查跳过（需要自定义函数）');
      } else {
        console.log('触发器状态:', triggers);
      }

    } catch (error) {
      console.error('❌ 数据库状态检查失败:', error);
    } finally {
      console.groupEnd();
    }
  }
}

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  (window as any).RegistrationDebug = RegistrationDebug;
  console.log('🔧 RegistrationDebug 已暴露到全局，可以在控制台使用');
}
