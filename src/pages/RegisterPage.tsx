import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Github, Mail, Lock, User, Eye, EyeOff } from 'lucide-react';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Card } from '../components/ui/Card';
import { WaveBackground } from '../components/animations/WaveBackground';
import { useAuth } from '../contexts/AuthContext';

export const RegisterPage: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const { register } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      console.log('Starting registration process...');
      const result = await register(email, password, name);

      if (result?.user) {
        if (result.user.email_confirmed_at) {
          // 邮箱已确认，直接跳转到仪表板
          console.log('Email confirmed, redirecting to dashboard');
          navigate('/dashboard');
        } else {
          // 需要邮箱确认
          console.log('Email confirmation required');
          setError('注册成功！请检查您的邮箱并点击确认链接以完成注册。');
          // 可以考虑跳转到一个确认页面
          // navigate('/email-confirmation');
        }
      } else {
        setError('注册过程中出现未知错误，请重试');
      }
    } catch (error) {
      console.error('Registration failed:', error);

      // 提供更友好的错误信息
      let errorMessage = '注册失败，请重试';

      if (error instanceof Error) {
        if (error.message.includes('already registered')) {
          errorMessage = '该邮箱已被注册，请使用其他邮箱或尝试登录';
        } else if (error.message.includes('Password')) {
          errorMessage = '密码格式不正确，请确保密码至少6位字符';
        } else if (error.message.includes('Email')) {
          errorMessage = '邮箱格式不正确，请检查邮箱地址';
        } else if (error.message.includes('Database error')) {
          errorMessage = '服务器暂时繁忙，请稍后重试';
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4 relative overflow-hidden">
      <WaveBackground />
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative z-10 w-full max-w-md"
      >
        <Card glass className="p-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <img src="/logo-32x32.png" alt="SoulVoice Logo" className="h-8 w-8" />
              <span className="text-2xl font-bold text-white">SoulVoice</span>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">开始您的语音之旅</h1>
            <p className="text-gray-400">创建您的 SoulVoice 账户</p>
          </div>

          {/* Social Login */}
          <div className="space-y-4 mb-6">
            <div className="text-center mb-2">
              <p className="text-xs text-gray-500">GitHub 注册功能正在维护中</p>
            </div>
            <Button
              variant="glass"
              className="w-full justify-center opacity-50 cursor-not-allowed"
              size="lg"
              disabled={true}
              title="GitHub 注册功能暂时不可用"
            >
              <Github className="h-5 w-5 mr-2" />
              使用 GitHub 注册（暂不可用）
            </Button>
          </div>

          <div className="relative mb-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-700"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-900 text-gray-400">或使用邮箱注册</span>
            </div>
          </div>

          {error && (
            <div className="mb-6 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="输入您的姓名"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="pl-10"
                required
              />
            </div>

            <div className="relative">
              <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <Input
                type="email"
                placeholder="输入您的邮箱地址"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="pl-10"
                required
              />
            </div>

            <div className="relative">
              <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder="创建您的密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-10 pr-10"
                required
              />
              <button
                type="button"
                className="absolute right-3 top-3 text-gray-400 hover:text-white"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>

            <div className="space-y-4">
              <label className="flex items-start space-x-2 text-sm text-gray-400">
                <input type="checkbox" className="mt-1 bg-gray-800 border-gray-700" required />
                <span>
                  我同意 SoulVoice 的{' '}
                  <Link to="/terms" className="text-purple-400 hover:text-purple-300">
                    服务条款
                  </Link>{' '}
                  和{' '}
                  <Link to="/privacy" className="text-purple-400 hover:text-purple-300">
                    隐私政策
                  </Link>
                </span>
              </label>
            </div>

            <Button
              type="submit"
              variant="primary"
              className="w-full"
              size="lg"
              glow
              disabled={isLoading}
            >
              {isLoading ? '注册中...' : '立即注册'}
            </Button>
          </form>

          <div className="text-center mt-6">
            <p className="text-gray-400">
              已有账户？{' '}
              <Link to="/login" className="text-purple-400 hover:text-purple-300">
                立即登录
              </Link>
            </p>
          </div>
        </Card>

        <div className="text-center mt-4">
          <Link to="/" className="text-gray-400 hover:text-white text-sm">
            ← 返回首页
          </Link>
        </div>
      </motion.div>
    </div>
  );
};