import { supabase } from '../lib/supabase';
import type { Profile } from '../lib/supabase';
import { ActivityService } from './activityService';

export class AuthService {
  // 注册用户
  static async signUp(email: string, password: string, name: string) {
    try {
      console.log('Starting user registration...', { email, name });

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            full_name: name, // 添加 full_name 以便触发器使用
          },
        },
      });

      if (error) {
        console.error('Supabase auth signup error:', error);
        throw error;
      }

      if (!data.user) {
        throw new Error('注册失败：未能创建用户');
      }

      console.log('User created successfully:', {
        id: data.user.id,
        email: data.user.email,
        confirmed: data.user.email_confirmed_at ? true : false
      });

      // 数据库触发器会自动创建用户资料、设置和余额
      // 不需要手动创建，避免冲突

      return data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // 验证用户资料是否已创建（用于调试）
  static async verifyUserProfile(userId: string) {
    try {
      console.log('Verifying user profile for:', userId);

      // 检查 profiles 表
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error checking profile:', profileError);
      }

      // 检查 user_settings 表
      const { data: settings, error: settingsError } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (settingsError && settingsError.code !== 'PGRST116') {
        console.error('Error checking settings:', settingsError);
      }

      // 检查 user_balances 表
      const { data: balance, error: balanceError } = await supabase
        .from('user_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (balanceError && balanceError.code !== 'PGRST116') {
        console.error('Error checking balance:', balanceError);
      }

      console.log('Profile verification result:', {
        profile: profile ? 'exists' : 'missing',
        settings: settings ? 'exists' : 'missing',
        balance: balance ? 'exists' : 'missing',
        balanceDetails: balance ? {
          totalBytes: balance.total_bytes,
          usedBytes: balance.used_bytes,
          freeBytes: balance.free_bytes,
          subscriptionLevel: balance.subscription_level
        } : null
      });

      return {
        profile,
        settings,
        balance
      };
    } catch (error) {
      console.error('Error verifying user profile:', error);
      return null;
    }
  }

  // 获取用户余额信息
  static async getUserBalance(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching user balance:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getUserBalance:', error);
      return null;
    }
  }

  // 登录用户
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    // 记录登录活动
    if (data.user) {
      try {
        await ActivityService.logAccountActivity(data.user.id, 'login');
      } catch (error) {
        console.error('Failed to log login activity:', error);
      }
    }

    return data;
  }

  // 使用 GitHub 登录
  static async signInWithGitHub() {
    // 获取当前环境的基础 URL
    const getRedirectUrl = () => {
      const origin = window.location.origin;
      return `${origin}/auth/callback`;
    };

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'github',
      options: {
        redirectTo: getRedirectUrl(),
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      // 处理账户冲突错误
      if (error.message?.includes('Multiple accounts') || error.message?.includes('linking domain')) {
        throw new Error('该邮箱已存在账户，请使用邮箱密码登录，或联系管理员合并账户');
      }
      throw error;
    }
    return data;
  }

  // 登出
  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  // 获取当前用户
  static async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }

  // 获取用户资料
  static async getProfile(userId: string): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    // 如果用户资料不存在，返回 null 而不是抛出错误
    if (error && error.code === 'PGRST116') {
      console.log('User profile not found, may need to create one');
      return null;
    }

    if (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }

    return data;
  }

  // 更新用户资料
  static async updateProfile(userId: string, updates: Partial<Profile>) {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
}