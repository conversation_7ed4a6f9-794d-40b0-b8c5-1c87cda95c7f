import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

export const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // 检查 URL 中是否有 access_token
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const searchParams = new URLSearchParams(window.location.search);
        
        const accessToken = hashParams.get('access_token') || searchParams.get('access_token');
        const error = hashParams.get('error') || searchParams.get('error');
        
        if (error) {
          console.error('OAuth error:', error);
          const errorDescription = hashParams.get('error_description') || searchParams.get('error_description');
          
          if (errorDescription?.includes('Multiple accounts')) {
            navigate('/login?error=account_conflict');
          } else {
            navigate('/login?error=oauth_failed');
          }
          return;
        }

        if (accessToken) {
          // Token 存在，等待 Supabase 处理认证
          console.log('OAuth token received, waiting for auth state...');
          
          // 等待一小段时间让 Supabase 处理认证
          setTimeout(() => {
            if (user) {
              navigate('/dashboard', { replace: true });
            } else {
              // 如果用户状态还没更新，再等一下
              setTimeout(() => {
                navigate('/dashboard', { replace: true });
              }, 1000);
            }
          }, 500);
        } else {
          // 没有 token，可能是直接访问了这个页面
          navigate('/login', { replace: true });
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        navigate('/login?error=callback_failed');
      }
    };

    handleAuthCallback();
  }, [navigate, user]);

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
        <p className="text-white text-lg mb-2">正在处理登录...</p>
        <p className="text-gray-400">请稍候，我们正在验证您的身份</p>
      </div>
    </div>
  );
};
