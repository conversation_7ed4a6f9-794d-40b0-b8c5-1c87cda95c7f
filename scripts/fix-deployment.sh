#!/bin/bash

# SoulVoice 部署修复脚本
# 用于修复 GitHub OAuth 登录后的 404 问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必需工具
check_requirements() {
    log_info "检查必需工具..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    log_success "所有必需工具已安装"
}

# 清理构建缓存
clean_build() {
    log_info "清理构建缓存..."
    
    # 删除 node_modules 和构建产物
    rm -rf node_modules
    rm -rf dist
    rm -rf .vercel
    
    log_success "构建缓存已清理"
}

# 重新安装依赖
install_dependencies() {
    log_info "重新安装依赖..."
    
    npm install
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist 目录不存在"
        exit 1
    fi
    
    log_success "项目构建完成"
}

# 验证构建产物
verify_build() {
    log_info "验证构建产物..."
    
    if [ ! -f "dist/index.html" ]; then
        log_error "index.html 文件不存在"
        exit 1
    fi
    
    # 检查是否包含必要的路由处理
    if ! grep -q "react-router" dist/assets/*.js 2>/dev/null; then
        log_warning "可能缺少 React Router 相关代码"
    fi
    
    log_success "构建产物验证通过"
}

# 部署到 Vercel
deploy_to_vercel() {
    log_info "部署到 Vercel..."
    
    # 检查是否安装了 Vercel CLI
    if ! command -v vercel &> /dev/null; then
        log_info "安装 Vercel CLI..."
        npm install -g vercel
    fi
    
    # 检查是否已登录
    if ! vercel whoami &> /dev/null; then
        log_info "请登录 Vercel..."
        vercel login
    fi
    
    # 部署项目
    if [ "$1" = "--prod" ]; then
        log_info "部署到生产环境..."
        vercel --prod --yes
    else
        log_info "部署到预览环境..."
        vercel --yes
    fi
    
    log_success "部署完成！"
}

# 显示后续步骤
show_next_steps() {
    echo ""
    echo "========================================"
    log_success "🎉 部署修复完成！"
    echo "========================================"
    echo ""
    log_info "接下来的步骤："
    echo "1. 等待 Vercel 部署完成（通常需要 1-2 分钟）"
    echo "2. 清除浏览器缓存"
    echo "3. 测试 GitHub 登录功能"
    echo "4. 如果仍有问题，检查 Vercel 部署日志"
    echo ""
    log_info "GitHub OAuth 回调 URL 应该设置为："
    echo "https://your-domain.com/auth/callback"
    echo ""
    log_info "Supabase Auth 重定向 URL 应该包含："
    echo "https://your-domain.com/auth/callback"
    echo "https://your-domain.com/dashboard"
}

# 主函数
main() {
    echo "========================================"
    echo "🔧 SoulVoice 部署修复脚本"
    echo "========================================"
    
    check_requirements
    clean_build
    install_dependencies
    build_project
    verify_build
    deploy_to_vercel $1
    show_next_steps
}

# 显示帮助信息
show_help() {
    echo "SoulVoice 部署修复脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --prod    部署到生产环境"
    echo "  --help    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0          # 部署到预览环境"
    echo "  $0 --prod   # 部署到生产环境"
}

# 处理命令行参数
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    *)
        main $1
        ;;
esac
